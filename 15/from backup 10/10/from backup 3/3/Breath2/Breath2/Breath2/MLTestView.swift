//
//  MLTestView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Test view for ML-enhanced breath detection and pitch control

import SwiftUI

struct MLTestView: View {
    @StateObject private var audioManager = AudioManager()
    @State private var isRecording = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Professional dark background
                LinearGradient(
                    colors: [
                        Color(red: 0.05, green: 0.05, blue: 0.08),
                        Color(red: 0.08, green: 0.08, blue: 0.12),
                        Color(red: 0.10, green: 0.10, blue: 0.15)
                    ],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // Header
                        headerView
                        
                        // ML Classification Status
                        mlStatusView
                        
                        // Breath Detection Status
                        breathDetectionView
                        
                        // Pitch Detection Status
                        pitchDetectionView
                        
                        // Control Buttons
                        controlButtonsView
                        
                        Spacer(minLength: 100)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 10)
                }
            }
            .navigationTitle("ML Test")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
        .onAppear {
            audioManager.requestPermission()
        }
    }
    
    // MARK: - Header View
    
    private var headerView: some View {
        VStack(spacing: 12) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 60))
                .foregroundStyle(
                    LinearGradient(
                        colors: [.cyan, .blue],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            Text("ML-Enhanced Breath Detection")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            Text("Testing sound classification model integration")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
        .padding(.vertical, 20)
    }
    
    // MARK: - ML Status View
    
    private var mlStatusView: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Sound Classification")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Circle()
                    .fill(audioManager.soundClassificationManager.isActive ? .green : .red)
                    .frame(width: 12, height: 12)
            }
            
            VStack(spacing: 12) {
                InfoRow(
                    title: "Classification",
                    value: audioManager.soundClassificationManager.currentClassification,
                    color: .cyan
                )
                
                InfoRow(
                    title: "Confidence",
                    value: "\(Int(audioManager.soundClassificationManager.currentConfidence * 100))%",
                    color: confidenceColor
                )
                
                InfoRow(
                    title: "Breath Detected",
                    value: audioManager.soundClassificationManager.isBreathDetected ? "YES" : "NO",
                    color: audioManager.soundClassificationManager.isBreathDetected ? .green : .red
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.cyan.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Breath Detection View
    
    private var breathDetectionView: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Breath Detection")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Text("\(audioManager.breathDetector.breathCount)")
                    .font(.title2)
                    .fontWeight(.bold)
                    .foregroundColor(.cyan)
            }
            
            VStack(spacing: 12) {
                InfoRow(
                    title: "State",
                    value: audioManager.breathDetector.currentState.description,
                    color: .orange
                )
                
                InfoRow(
                    title: "ML Enhanced",
                    value: audioManager.breathDetector.mlBreathDetected ? "ACTIVE" : "INACTIVE",
                    color: audioManager.breathDetector.mlBreathDetected ? .green : .gray
                )
                
                InfoRow(
                    title: "Duration",
                    value: String(format: "%.1fs", audioManager.breathDetector.currentBreathDuration),
                    color: .blue
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Pitch Detection View
    
    private var pitchDetectionView: some View {
        VStack(spacing: 16) {
            HStack {
                Text("Pitch Detection")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                
                Spacer()
                
                Circle()
                    .fill(audioManager.soundClassificationManager.isBreathDetected ? .green : .red)
                    .frame(width: 12, height: 12)
            }
            
            VStack(spacing: 12) {
                InfoRow(
                    title: "Frequency",
                    value: String(format: "%.1f Hz", audioManager.currentFrequency),
                    color: .green
                )
                
                InfoRow(
                    title: "Pressure",
                    value: String(format: "%.1f cm H2O", audioManager.currentPressure),
                    color: .blue
                )
                
                InfoRow(
                    title: "ML Controlled",
                    value: audioManager.soundClassificationManager.isBreathDetected ? "ENABLED" : "DISABLED",
                    color: audioManager.soundClassificationManager.isBreathDetected ? .green : .red
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white.opacity(0.05))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.green.opacity(0.3), lineWidth: 1)
                )
        )
    }
    
    // MARK: - Control Buttons View
    
    private var controlButtonsView: some View {
        VStack(spacing: 16) {
            Button(action: {
                if isRecording {
                    audioManager.stopRecording()
                } else {
                    audioManager.startRecording()
                }
                isRecording.toggle()
            }) {
                HStack {
                    Image(systemName: isRecording ? "stop.circle.fill" : "play.circle.fill")
                        .font(.title2)
                    
                    Text(isRecording ? "Stop Testing" : "Start Testing")
                        .font(.headline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isRecording ? .red : .cyan)
                )
            }
            
            Text("The ML model will control when pitch detection runs based on breath sounds (aPx patterns)")
                .font(.caption)
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
                .padding(.horizontal, 20)
        }
    }
    
    // MARK: - Computed Properties
    
    private var confidenceColor: Color {
        let confidence = audioManager.soundClassificationManager.currentConfidence
        if confidence >= 0.85 { return .green }
        else if confidence >= 0.7 { return .yellow }
        else if confidence >= 0.5 { return .orange }
        else { return .red }
    }
}

// MARK: - Supporting Views

struct InfoRow: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.8))
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(color)
        }
    }
}

// MARK: - Extensions

extension BreathState {
    var description: String {
        switch self {
        case .idle: return "Idle"
        case .starting: return "Starting"
        case .active: return "Active"
        case .ending: return "Ending"
        case .completed: return "Completed"
        }
    }
}

#Preview {
    MLTestView()
}
