//
//  SoundClassificationManager.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//
//  Manages sound classification using Core ML model for breath detection
//  Controls when to count breaths and when to feed data to pitch detection

import Foundation
import SoundAnalysis
import CoreML
import AVFoundation
import Combine

// MARK: - Sound Classification Results

/// Represents a sound classification result with confidence
struct SoundClassificationResult {
    let identifier: String
    let confidence: Float
    let timeRange: CMTimeRange
    let timestamp: Date
    
    /// Check if this is a breath sound (aPx pattern) with valid confidence range
    var isBreathSound: Bool {
        return identifier.lowercased().hasPrefix("ap") && isValidConfidenceRange
    }

    /// Check if confidence is in the valid range (20% to 80%)
    var isValidConfidenceRange: Bool {
        return confidence >= 0.20 && confidence <= 0.80
    }
}

// MARK: - Sound Classification Delegate

protocol SoundClassificationDelegate: AnyObject {
    /// Called when breath sound is detected with high confidence
    func soundClassificationDidDetectBreath(_ result: SoundClassificationResult)
    
    /// Called when breath sound confidence drops below threshold
    func soundClassificationDidLoseBreath(_ result: SoundClassificationResult)
    
    /// Called when classification fails
    func soundClassificationDidFail(with error: Error)
}

// MARK: - Sound Classification Manager

class SoundClassificationManager: NSObject, ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var isActive: Bool = false
    @Published var currentClassification: String = "Unknown"
    @Published var currentConfidence: Float = 0.0
    @Published var isBreathDetected: Bool = false
    @Published var lastBreathTime: Date?
    
    // MARK: - Private Properties
    
    private var soundClassifyRequest: SNClassifySoundRequest?
    private var streamAnalyzer: SNAudioStreamAnalyzer?
    private weak var delegate: SoundClassificationDelegate?
    
    // State tracking
    private var wasBreathDetected: Bool = false
    private var lastClassificationTime: Date = Date()
    
    // Configuration
    private let confidenceRangeMin: Float = 0.20
    private let confidenceRangeMax: Float = 0.80
    private let modelName = "MySoundClassifier3 1"
    
    // MARK: - Initialization
    
    override init() {
        super.init()
        setupSoundClassification()
    }
    
    // MARK: - Public Methods
    
    /// Set the delegate to receive classification callbacks
    func setDelegate(_ delegate: SoundClassificationDelegate) {
        self.delegate = delegate
    }
    
    /// Start sound classification with the given audio format
    func startClassification(with format: AVAudioFormat) throws {
        guard let request = soundClassifyRequest else {
            throw SoundClassificationError.modelNotLoaded
        }
        
        // Create stream analyzer
        streamAnalyzer = SNAudioStreamAnalyzer(format: format)
        
        // Add classification request
        try streamAnalyzer?.add(request, withObserver: self)
        
        isActive = true
        print("🎵 Sound classification started")
    }
    
    /// Stop sound classification
    func stopClassification() {
        streamAnalyzer?.removeAllRequests()
        streamAnalyzer = nil
        isActive = false
        isBreathDetected = false
        wasBreathDetected = false
        
        print("🛑 Sound classification stopped")
    }
    
    /// Process audio buffer for classification
    func processAudioBuffer(_ buffer: AVAudioPCMBuffer, atTime time: AVAudioTime) {
        guard isActive, let analyzer = streamAnalyzer else {
            print("🚫 Sound analysis not active or analyzer missing")
            return
        }

        print("🎵 Processing audio buffer for ML analysis (frames: \(buffer.frameLength))")
        analyzer.analyze(buffer, atAudioFramePosition: time.sampleTime)
    }
    
    // MARK: - Private Methods
    
    private func setupSoundClassification() {
        do {
            // Load the Core ML model - try compiled version first, then source
            var modelURL: URL?

            // First try to find the compiled model (.mlmodelc)
            if let compiledURL = Bundle.main.url(forResource: modelName, withExtension: "mlmodelc") {
                modelURL = compiledURL
                print("🎯 Found compiled model: \(modelName).mlmodelc")
            }
            // Fallback to source model (.mlmodel)
            else if let sourceURL = Bundle.main.url(forResource: modelName, withExtension: "mlmodel") {
                modelURL = sourceURL
                print("🎯 Found source model: \(modelName).mlmodel")
            }

            guard let url = modelURL else {
                print("❌ Could not find model file: \(modelName).mlmodel or \(modelName).mlmodelc")
                return
            }

            let mlModel = try MLModel(contentsOf: url)
            print("🔍 Model description: \(mlModel.modelDescription)")
            print("🔍 Model inputs: \(mlModel.modelDescription.inputDescriptionsByName)")
            print("🔍 Model outputs: \(mlModel.modelDescription.outputDescriptionsByName)")

            soundClassifyRequest = try SNClassifySoundRequest(mlModel: mlModel)

            print("✅ Sound classification model loaded successfully from: \(url.lastPathComponent)")

        } catch {
            print("❌ Failed to load sound classification model: \(error)")
        }
    }
    
    private func handleClassificationResult(_ result: SoundClassificationResult) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            // Update published properties
            self.currentClassification = result.identifier
            self.currentConfidence = result.confidence
            self.lastClassificationTime = result.timestamp
            
            // Check for breath detection state changes
            let isCurrentlyBreath = result.isBreathSound
            
            if isCurrentlyBreath && !self.wasBreathDetected {
                // Breath started
                self.isBreathDetected = true
                self.wasBreathDetected = true
                self.lastBreathTime = result.timestamp
                self.delegate?.soundClassificationDidDetectBreath(result)
                
                print("🫁 Breath detected: \(result.identifier) (\(Int(result.confidence * 100))%)")
                
            } else if !isCurrentlyBreath && self.wasBreathDetected {
                // Breath ended
                self.isBreathDetected = false
                self.wasBreathDetected = false
                self.delegate?.soundClassificationDidLoseBreath(result)
                
                print("🫁 Breath ended: \(result.identifier) (\(Int(result.confidence * 100))%)")
            }
        }
    }
}

// MARK: - SNResultsObserving

extension SoundClassificationManager: SNResultsObserving {
    
    func request(_ request: SNRequest, didProduce result: SNResult) {
        print("🔍 Sound Analysis result received: \(type(of: result))")

        guard let classificationResult = result as? SNClassificationResult else {
            print("❌ Result is not SNClassificationResult: \(type(of: result))")
            return
        }

        print("🔍 Classifications count: \(classificationResult.classifications.count)")

        guard let classification = classificationResult.classifications.first else {
            print("❌ No classifications found in result")
            return
        }

        print("🔍 Raw classification: \(classification.identifier) (\(classification.confidence))")

        let soundResult = SoundClassificationResult(
            identifier: classification.identifier,
            confidence: Float(classification.confidence),
            timeRange: classificationResult.timeRange,
            timestamp: Date()
        )

        handleClassificationResult(soundResult)
    }
    
    func request(_ request: SNRequest, didFailWithError error: Error) {
        print("❌ Sound classification failed: \(error)")
        
        DispatchQueue.main.async { [weak self] in
            self?.delegate?.soundClassificationDidFail(with: error)
        }
    }
    
    func requestDidComplete(_ request: SNRequest) {
        print("✅ Sound classification completed")
    }
}

// MARK: - Errors

enum SoundClassificationError: Error, LocalizedError {
    case modelNotLoaded
    case analysisNotStarted
    case invalidAudioFormat
    
    var errorDescription: String? {
        switch self {
        case .modelNotLoaded:
            return "Sound classification model could not be loaded"
        case .analysisNotStarted:
            return "Sound analysis has not been started"
        case .invalidAudioFormat:
            return "Invalid audio format for sound analysis"
        }
    }
}
