//
//  TherapySettingsView.swift
//  Breath2
//
//  Created by <PERSON> on 02/06/2025.
//

import SwiftUI

struct TherapySettingsView: View {
    @ObservedObject var configuration: TherapyConfiguration
    @Environment(\.dismiss) private var dismiss
    @State private var selectedPreset: PresetType = .custom
    @State private var showingCustomEditor = false
    
    enum PresetType: String, CaseIterable {
        case single = "Single Session"
        case double = "Two Sessions"
        case triple = "Three Sessions"
        case custom = "Custom"
        
        var description: String {
            switch self {
            case .single: return "1 session of 10 exhalations"
            case .double: return "2 sessions of 15 exhalations each"
            case .triple: return "3 sessions of 10 exhalations each"
            case .custom: return "Customize your own routine"
            }
        }
        
        var icon: String {
            switch self {
            case .single: return "1.circle.fill"
            case .double: return "2.circle.fill"
            case .triple: return "3.circle.fill"
            case .custom: return "slider.horizontal.3"
            }
        }
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    headerSection
                    presetSection
                    currentConfigurationSection
                    limitsSection
                }
                .padding()
            }
            .navigationTitle("Therapy Settings")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    // MARK: - Header Section
    
    private var headerSection: some View {
        VStack(spacing: 12) {
            Image(systemName: "lungs.fill")
                .font(.system(size: 40))
                .foregroundColor(.blue)
            
            Text("Daily Therapy Routine")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Customize your PEP therapy sessions to fit your schedule")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.7))
                .multilineTextAlignment(.center)
        }
        .padding(.vertical)
    }
    
    // MARK: - Preset Section
    
    private var presetSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Setup")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(PresetType.allCases, id: \.self) { preset in
                    PresetCard(
                        preset: preset,
                        isSelected: selectedPreset == preset,
                        action: { selectPreset(preset) }
                    )
                }
            }
        }
    }
    
    // MARK: - Current Configuration Section
    
    private var currentConfigurationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Current Configuration")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if selectedPreset == .custom {
                    Button("Edit") {
                        showingCustomEditor = true
                    }
                    .font(.subheadline)
                    .fontWeight(.medium)
                }
            }
            
            ConfigurationSummaryCard(configuration: configuration)
        }
        .sheet(isPresented: $showingCustomEditor) {
            CustomConfigurationEditor(configuration: configuration)
        }
    }
    
    // MARK: - Limits Section
    
    private var limitsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Guidelines")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 8) {
                LimitRow(
                    icon: "calendar",
                    title: "Maximum per day",
                    value: "\(TherapyConfiguration.maxExhalationsPerDay) exhalations"
                )
                
                LimitRow(
                    icon: "clock",
                    title: "Sessions per day",
                    value: "Up to \(TherapyConfiguration.maxSessionsPerDay)"
                )
                
                LimitRow(
                    icon: "timer",
                    title: "Per session",
                    value: "\(TherapyConfiguration.minExhalationsPerBlock)-\(TherapyConfiguration.maxExhalationsPerBlock) exhalations"
                )
            }
        }
    }
    
    // MARK: - Actions
    
    private func selectPreset(_ preset: PresetType) {
        selectedPreset = preset
        
        switch preset {
        case .single:
            configuration.dailyBlocks = TherapyConfiguration.singleSession().dailyBlocks
        case .double:
            configuration.dailyBlocks = TherapyConfiguration.twoSessions().dailyBlocks
        case .triple:
            configuration.dailyBlocks = TherapyConfiguration.threeSessions().dailyBlocks
        case .custom:
            break // Keep current configuration
        }
        
        configuration.resetDailyProgress()
    }
}

// MARK: - Preset Card

struct PresetCard: View {
    let preset: TherapySettingsView.PresetType
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                Image(systemName: preset.icon)
                    .font(.system(size: 24))
                    .foregroundColor(isSelected ? .white : .blue)
                
                VStack(spacing: 4) {
                    Text(preset.rawValue)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(isSelected ? .white : .primary)
                    
                    Text(preset.description)
                        .font(.caption)
                        .foregroundColor(isSelected ? .white.opacity(0.8) : .white.opacity(0.7))
                        .multilineTextAlignment(.center)
                }
            }
            .padding()
            .frame(maxWidth: .infinity)
            .frame(height: 120)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(isSelected ? Color.blue : Color(.systemGray6))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Configuration Summary Card

struct ConfigurationSummaryCard: View {
    @ObservedObject var configuration: TherapyConfiguration
    
    var body: some View {
        VStack(spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(configuration.dailyBlocks.count)")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)
                    Text("Sessions")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(configuration.totalExhalationsToday)")
                        .font(.title)
                        .fontWeight(.bold)
                        .foregroundColor(.green)
                    Text("Total Exhalations")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
            }
            
            Divider()
            
            VStack(spacing: 8) {
                ForEach(configuration.dailyBlocks) { block in
                    SessionRow(block: block)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Session Row

struct SessionRow: View {
    let block: TherapyBlock

    var body: some View {
        HStack(spacing: 12) {
            // Status indicator
            Circle()
                .fill(block.isCompleted ? Color.green : Color.blue.opacity(0.6))
                .frame(width: 10, height: 10)

            // Session info
            VStack(alignment: .leading, spacing: 2) {
                Text(block.name)
                    .font(.subheadline)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Text("\(block.exhalations) exhalations")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }

            Spacer()

            // Time estimate with icon
            HStack(spacing: 4) {
                Image(systemName: "clock")
                    .font(.caption)
                    .foregroundColor(.orange)

                Text(block.formattedDuration)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.orange)
            }
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(Color.orange.opacity(0.1))
            .cornerRadius(6)
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Limit Row

struct LimitRow: View {
    let icon: String
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .font(.subheadline)
                .foregroundColor(.blue)
                .frame(width: 20)
            
            Text(title)
                .font(.subheadline)
            
            Spacer()
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.white.opacity(0.7))
        }
    }
}

// MARK: - Custom Configuration Editor

struct CustomConfigurationEditor: View {
    @ObservedObject var configuration: TherapyConfiguration
    @Environment(\.dismiss) private var dismiss
    @State private var editableBlocks: [TherapyBlock] = []
    @State private var showingAlert = false
    @State private var alertMessage = ""

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    headerSection
                    blocksSection
                    addSessionButton
                    summarySection
                }
                .padding()
            }
            .navigationTitle("Custom Setup")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(Color(red: 0.05, green: 0.05, blue: 0.08), for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveConfiguration()
                    }
                    .fontWeight(.semibold)
                    .disabled(!isValidConfiguration)
                }
            }
        }
        .onAppear {
            editableBlocks = configuration.dailyBlocks
        }
        .alert("Invalid Configuration", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
    }

    private var headerSection: some View {
        VStack(spacing: 8) {
            Text("Design Your Routine")
                .font(.title2)
                .fontWeight(.bold)

            Text("Create a personalized therapy schedule")
                .font(.subheadline)
                .foregroundColor(.white.opacity(0.7))
        }
    }

    private var blocksSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Therapy Sessions")
                .font(.headline)
                .fontWeight(.semibold)

            ForEach(editableBlocks.indices, id: \.self) { index in
                EditableSessionCard(
                    block: $editableBlocks[index],
                    onDelete: { deleteSession(at: index) }
                )
            }
        }
    }

    private var addSessionButton: some View {
        Button(action: addSession) {
            HStack {
                Image(systemName: "plus.circle.fill")
                    .font(.title2)

                Text("Add Session")
                    .fontWeight(.medium)
            }
            .foregroundColor(.blue)
            .padding()
            .frame(maxWidth: .infinity)
            .background(Color.blue.opacity(0.1))
            .cornerRadius(12)
        }
        .disabled(editableBlocks.count >= TherapyConfiguration.maxSessionsPerDay)
    }

    private var summarySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Summary")
                .font(.headline)
                .fontWeight(.semibold)

            SummaryCard(blocks: editableBlocks)
        }
    }

    private var isValidConfiguration: Bool {
        let totalExhalations = editableBlocks.reduce(0) { $0 + $1.exhalations }
        return !editableBlocks.isEmpty &&
               editableBlocks.count <= TherapyConfiguration.maxSessionsPerDay &&
               totalExhalations <= TherapyConfiguration.maxExhalationsPerDay &&
               editableBlocks.allSatisfy { block in
                   block.exhalations >= TherapyConfiguration.minExhalationsPerBlock &&
                   block.exhalations <= TherapyConfiguration.maxExhalationsPerBlock
               }
    }

    private func addSession() {
        let newId = (editableBlocks.map { $0.id }.max() ?? 0) + 1
        let sessionNumber = editableBlocks.count + 1
        let newBlock = TherapyBlock(
            id: newId,
            exhalations: 10,
            name: "Session \(sessionNumber)"
        )
        editableBlocks.append(newBlock)
    }

    private func deleteSession(at index: Int) {
        editableBlocks.remove(at: index)
        // Renumber sessions
        for i in editableBlocks.indices {
            editableBlocks[i].name = "Session \(i + 1)"
        }
    }

    private func saveConfiguration() {
        if isValidConfiguration {
            configuration.dailyBlocks = editableBlocks
            configuration.resetDailyProgress()
            dismiss()
        } else {
            alertMessage = "Please ensure your configuration meets the guidelines."
            showingAlert = true
        }
    }
}

// MARK: - Editable Session Card

struct EditableSessionCard: View {
    @Binding var block: TherapyBlock
    let onDelete: () -> Void

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                TextField("Session Name", text: $block.name)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()

                Button(action: onDelete) {
                    Image(systemName: "trash")
                        .foregroundColor(.red)
                }
            }

            HStack {
                Text("Exhalations:")
                    .font(.subheadline)

                Spacer()

                Stepper(
                    value: $block.exhalations,
                    in: TherapyConfiguration.minExhalationsPerBlock...TherapyConfiguration.maxExhalationsPerBlock
                ) {
                    Text("\(block.exhalations)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .frame(minWidth: 30)
                }
            }

            HStack {
                Text("Estimated duration:")
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))

                Spacer()

                Text(block.formattedDuration)
                    .font(.caption)
                    .foregroundColor(.white.opacity(0.7))
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - Summary Card

struct SummaryCard: View {
    let blocks: [TherapyBlock]

    private var totalExhalations: Int {
        blocks.reduce(0) { $0 + $1.exhalations }
    }

    private var totalDuration: TimeInterval {
        blocks.reduce(0) { $0 + $1.estimatedDuration }
    }

    private var isValid: Bool {
        totalExhalations <= TherapyConfiguration.maxExhalationsPerDay &&
        blocks.count <= TherapyConfiguration.maxSessionsPerDay
    }

    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("\(blocks.count)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(isValid ? .blue : .red)
                    Text("Sessions")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(totalExhalations)")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(isValid ? .green : .red)
                    Text("Total Exhalations")
                        .font(.caption)
                        .foregroundColor(.white.opacity(0.7))
                }
            }

            if !isValid {
                Text("Configuration exceeds daily limits")
                    .font(.caption)
                    .foregroundColor(.red)
                    .padding(.horizontal)
                    .padding(.vertical, 4)
                    .background(Color.red.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

#Preview {
    TherapySettingsView(configuration: TherapyConfiguration())
}
