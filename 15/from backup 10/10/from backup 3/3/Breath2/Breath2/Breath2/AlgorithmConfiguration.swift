//
//  AlgorithmConfiguration.swift
//  Breath2
//
//  Created by Mo on 02/06/2025.
//
//  Centralized configuration for all algorithm parameters
//  This file contains all tunable settings in one place for easy adjustment and testing

import Foundation

// MARK: - Configuration Presets Enum

enum ConfigurationPreset: String, CaseIterable {
    case standard = "Standard"
    case realTimeFeedback = "Real-Time Feedback"
    case highAccuracy = "High Accuracy"
    case stableReadings = "Stable Readings"
    case lowPower = "Low Power"
    case noisyEnvironment = "Noisy Environment"
    case pediatric = "Pediatric Therapy"
    case elderly = "Elderly/COPD"
    case athletic = "Athletic/High-Performance"
    case research = "Research/Clinical"
    case custom = "Custom"
}

// MARK: - Algorithm Configuration Structure

struct AlgorithmConfiguration: Codable {
    
    // MARK: - Audio Input Parameters
    
    /// Audio sampling frequency (Hz)
    /// Paper spec: 44,100 Hz or 48,000 Hz
    /// Impact: Higher = better accuracy, more CPU load
    var sampleRate: Float = 44100.0
    
    /// Audio buffer size in seconds
    /// Paper spec: 0.1 seconds recommended
    /// Impact: Smaller = faster updates, less accuracy
    var bufferSize: Float = 0.1
    
    // MARK: - Frequency Detection Range
    
    /// Minimum detectable frequency (Hz)
    /// Paper spec: 10 Hz, Current: 7 Hz (extended)
    /// Impact: Lower = more false positives, better low-pressure detection
    var minFreq: Int = 7
    
    /// Maximum detectable frequency (Hz)
    /// Paper spec: 40 Hz
    /// Impact: Higher = more noise sensitivity
    var maxFreq: Int = 40
    
    /// Frequency accuracy target (percentage)
    /// Paper spec: 0.025 (2.5%)
    /// Impact: Lower = more precise, higher CPU load
    var freqAccuracy: Float = 0.025
    
    // MARK: - Signal Processing Parameters
    
    /// Lower formant frequency for smoothing filter (Hz)
    /// Paper spec: 250 Hz
    /// Impact: Affects smoothing filter size calculation
    var lowerFormantFreq: Int = 250
    
    /// Minimum amplitude threshold
    /// Paper spec: 2.0E-4
    /// Impact: Filters out very weak signals
    var minAmp: Float = 2.0e-4
    
    /// Downsample factor (calculated automatically but can be overridden)
    /// Paper spec: 45 (44.1kHz → 980Hz)
    /// Impact: Higher = faster processing, lower accuracy
    var downsampleFactorOverride: Int? = nil
    
    // MARK: - Autocorrelation Settings
    
    /// Correlation threshold for pitch detection
    /// Paper spec: 0.6
    /// Impact: Higher = fewer false positives, may miss valid detections
    var correlationThreshold: Float = 0.6
    
    /// Step size for coarse autocorrelation search
    /// Paper spec: "rough resolution"
    /// Impact: Larger = faster search, may miss peaks
    var coarseStep: Int = 3
    
    /// Window size for fine autocorrelation search (±samples)
    /// Paper spec: "finer resolution"
    /// Impact: Larger = more thorough, slower processing
    var fineSearchWindow: Int = 5
    
    // MARK: - Moving Average Parameters
    
    /// Decay rate for moving averages
    /// Paper spec: 0.8
    /// Impact: Higher = smoother output, slower adaptation
    var decayRate: Float = 0.8
    
    /// Maximum run length for moving averages
    /// Paper spec: 5 (calculated from decay rate)
    /// Impact: Higher = more stable readings, slower adaptation
    var maxRunLength: Int = 5
    
    /// Leap threshold for run length reduction (percentage)
    /// Paper spec: "leap exceeds expected amount"
    /// Impact: Lower = more sensitive to changes
    var leapThreshold: Float = 0.20
    
    // MARK: - Buffer Management
    
    /// Target buffer length for pitch detection (samples at downsampled rate)
    /// Not specified in paper
    /// Impact: Larger = more accurate, higher latency
    var targetBufferLength: Int = 300
    
    /// Minimum data required before attempting detection
    /// Not specified in paper
    /// Impact: Higher = more reliable startup, longer initial delay
    var minDataCheck: Int = 100
    
    // MARK: - Pressure Model Settings
    
    /// Linear model slope (cm H2O per Hz)
    /// Paper spec: 1.119 (from research data)
    /// ⚠️ WARNING: Do not modify without clinical validation
    var pressureSlope: Float = 1.119
    
    /// Linear model intercept (cm H2O)
    /// Paper spec: -4.659 (from research data)
    /// ⚠️ WARNING: Do not modify without clinical validation
    var pressureIntercept: Float = -4.659
    
    /// Minimum valid frequency for pressure calculation (Hz)
    var minValidFreq: Float = 7.0
    
    /// Maximum valid frequency for pressure calculation (Hz)
    var maxValidFreq: Float = 40.0
    
    /// Minimum valid pressure output (cm H2O)
    var minValidPressure: Float = 6.0
    
    /// Maximum valid pressure output (cm H2O)
    var maxValidPressure: Float = 30.0
    
    // MARK: - Advanced Settings
    
    /// Enable result saving for debugging/research
    var saveResults: Bool = false
    
    /// Custom smoothing filter size override
    /// If nil, calculated as sampleRate/lowerFormantFreq
    var smoothingFilterSizeOverride: Int? = nil
    
    /// Custom Gaussian sigma override
    /// If nil, calculated as 0.2 * downsampleFactor * maxFreq / sampleRate
    var gaussianSigmaOverride: Float? = nil
    
    // MARK: - Computed Properties (not stored)

    /// Calculated downsample factor
    func getDownsampleFactor() -> Int {
        return downsampleFactorOverride ?? Int(round(sampleRate / 980.0))
    }

    /// Calculated downsampled rate
    func getDownsampledRate() -> Float {
        return sampleRate / Float(getDownsampleFactor())
    }

    /// Calculated smoothing filter size
    func getSmoothingFilterSize() -> Int {
        return smoothingFilterSizeOverride ?? Int(round(sampleRate / Float(lowerFormantFreq)))
    }

    /// Calculated Gaussian sigma
    func getGaussianSigma() -> Float {
        return gaussianSigmaOverride ?? (0.2 * Float(getDownsampleFactor()) * Float(maxFreq) / sampleRate)
    }

    /// Buffer size in samples
    func getBufferSizeInSamples() -> Int {
        return Int(sampleRate * bufferSize)
    }

    // MARK: - Computed Properties (for backward compatibility)

    var downsampleFactor: Int { getDownsampleFactor() }
    var downsampledRate: Float { getDownsampledRate() }
    var smoothingFilterSize: Int { getSmoothingFilterSize() }
    var gaussianSigma: Float { getGaussianSigma() }
    var bufferSizeInSamples: Int { getBufferSizeInSamples() }
    
    // MARK: - Validation
    
    /// Validates all parameters for consistency and safety
    func validate() -> (isValid: Bool, errors: [String]) {
        var errors: [String] = []
        
        // Sample rate validation
        if sampleRate < Float(2 * maxFreq) {
            errors.append("Sample rate (\(sampleRate)) must be at least 2x max frequency (\(maxFreq))")
        }
        
        // Buffer size validation
        if bufferSize <= 0 || bufferSize > 1.0 {
            errors.append("Buffer size (\(bufferSize)) must be between 0 and 1.0 seconds")
        }
        
        // Frequency range validation
        if minFreq <= 0 || minFreq >= maxFreq {
            errors.append("Frequency range invalid: minFreq (\(minFreq)) must be positive and less than maxFreq (\(maxFreq))")
        }
        
        // Correlation threshold validation
        if correlationThreshold < 0.0 || correlationThreshold > 1.0 {
            errors.append("Correlation threshold (\(correlationThreshold)) must be between 0.0 and 1.0")
        }
        
        // Decay rate validation
        if decayRate <= 0.0 || decayRate >= 1.0 {
            errors.append("Decay rate (\(decayRate)) must be between 0.0 and 1.0")
        }
        
        // Downsample validation
        if getDownsampledRate() < Float(2 * maxFreq) {
            errors.append("Downsampled rate (\(getDownsampledRate())) must satisfy Nyquist criterion for max frequency (\(maxFreq))")
        }
        
        // Search parameter validation
        if coarseStep <= 0 {
            errors.append("Coarse step (\(coarseStep)) must be positive")
        }
        
        if fineSearchWindow <= 0 {
            errors.append("Fine search window (\(fineSearchWindow)) must be positive")
        }
        
        // Buffer management validation
        if targetBufferLength <= 0 {
            errors.append("Target buffer length (\(targetBufferLength)) must be positive")
        }
        
        if minDataCheck <= 0 {
            errors.append("Min data check (\(minDataCheck)) must be positive")
        }
        
        return (errors.isEmpty, errors)
    }
    
    // MARK: - Description
    
    /// Human-readable description of current configuration
    var description: String {
        return """
        Algorithm Configuration:
        - Sample Rate: \(sampleRate) Hz
        - Buffer Size: \(bufferSize) sec (\(getBufferSizeInSamples()) samples)
        - Frequency Range: \(minFreq)-\(maxFreq) Hz
        - Downsample Factor: \(getDownsampleFactor()) (\(sampleRate)Hz → \(getDownsampledRate())Hz)
        - Correlation Threshold: \(correlationThreshold)
        - Decay Rate: \(decayRate)
        - Target Buffer Length: \(targetBufferLength) samples
        - Smoothing Filter Size: \(getSmoothingFilterSize()) samples
        """
    }
}

// MARK: - Preset Configurations

extension AlgorithmConfiguration {
    
    /// Standard configuration (paper specifications)
    static let standard = AlgorithmConfiguration()
    
    /// Real-time feedback mode (faster updates, lower latency)
    static let realTimeFeedback = AlgorithmConfiguration(
        sampleRate: 44100.0,
        bufferSize: 0.05,
        minFreq: 7,
        maxFreq: 40,
        freqAccuracy: 0.025,
        lowerFormantFreq: 250,
        minAmp: 2.0e-4,
        downsampleFactorOverride: nil,
        correlationThreshold: 0.5,
        coarseStep: 2,
        fineSearchWindow: 5,
        decayRate: 0.7,
        maxRunLength: 5,
        leapThreshold: 0.20,
        targetBufferLength: 200
    )
    
    /// High accuracy mode (better precision, higher latency)
    static let highAccuracy = AlgorithmConfiguration(
        sampleRate: 44100.0,
        bufferSize: 0.2,
        minFreq: 7,
        maxFreq: 40,
        freqAccuracy: 0.025,
        lowerFormantFreq: 250,
        minAmp: 2.0e-4,
        downsampleFactorOverride: 35,
        correlationThreshold: 0.7,
        coarseStep: 3,
        fineSearchWindow: 7,
        decayRate: 0.8,
        maxRunLength: 5,
        leapThreshold: 0.20,
        targetBufferLength: 500
    )
    
    /// Stable readings mode (smooth output, slower response)
    static let stableReadings = AlgorithmConfiguration(
        sampleRate: 44100.0,
        bufferSize: 0.1,
        minFreq: 7,
        maxFreq: 40,
        freqAccuracy: 0.025,
        lowerFormantFreq: 250,
        minAmp: 2.0e-4,
        downsampleFactorOverride: nil,
        correlationThreshold: 0.6,
        coarseStep: 3,
        fineSearchWindow: 5,
        decayRate: 0.9,
        maxRunLength: 7,
        leapThreshold: 0.15,
        targetBufferLength: 300,
        minDataCheck: 100,
        pressureSlope: 1.119,
        pressureIntercept: -4.659,
        minValidFreq: 7.0,
        maxValidFreq: 40.0,
        minValidPressure: 6.0,
        maxValidPressure: 30.0,
        saveResults: false,
        smoothingFilterSizeOverride: 250
    )
    
    /// Low power mode (reduced CPU usage)
    static let lowPower = AlgorithmConfiguration(
        sampleRate: 22050,
        bufferSize: 0.15,
        minFreq: 7,
        maxFreq: 40,
        freqAccuracy: 0.025,
        lowerFormantFreq: 250,
        minAmp: 2.0e-4,
        downsampleFactorOverride: 60,
        correlationThreshold: 0.6,
        coarseStep: 4,
        fineSearchWindow: 5,
        decayRate: 0.8,
        maxRunLength: 5,
        leapThreshold: 0.20,
        targetBufferLength: 250
    )
    
    /// Noisy environment mode (better noise rejection)
    static let noisyEnvironment = AlgorithmConfiguration(
        sampleRate: 44100.0,
        bufferSize: 0.1,
        minFreq: 10,
        maxFreq: 35,
        freqAccuracy: 0.025,
        lowerFormantFreq: 250,
        minAmp: 2.0e-4,
        downsampleFactorOverride: nil,
        correlationThreshold: 0.7,
        coarseStep: 3,
        fineSearchWindow: 5,
        decayRate: 0.8,
        maxRunLength: 5,
        leapThreshold: 0.15,
        targetBufferLength: 300,
        minDataCheck: 100,
        pressureSlope: 1.119,
        pressureIntercept: -4.659,
        minValidFreq: 7.0,
        maxValidFreq: 40.0,
        minValidPressure: 6.0,
        maxValidPressure: 30.0,
        saveResults: false,
        smoothingFilterSizeOverride: 300
    )
    
    /// Pediatric therapy (optimized for children)
    static let pediatric = AlgorithmConfiguration(
        sampleRate: 44100.0,
        bufferSize: 0.08,
        minFreq: 5,
        maxFreq: 30,
        freqAccuracy: 0.025,
        lowerFormantFreq: 250,
        minAmp: 2.0e-4,
        downsampleFactorOverride: nil,
        correlationThreshold: 0.55,
        coarseStep: 3,
        fineSearchWindow: 5,
        decayRate: 0.75,
        maxRunLength: 5,
        leapThreshold: 0.20,
        targetBufferLength: 300
    )
    
    /// Elderly/COPD patients (stability focus)
    static let elderly = AlgorithmConfiguration(
        sampleRate: 44100.0,
        bufferSize: 0.1,
        minFreq: 8,
        maxFreq: 35,
        freqAccuracy: 0.025,
        lowerFormantFreq: 250,
        minAmp: 2.0e-4,
        downsampleFactorOverride: nil,
        correlationThreshold: 0.65,
        coarseStep: 3,
        fineSearchWindow: 5,
        decayRate: 0.85,
        maxRunLength: 7,
        leapThreshold: 0.12,
        targetBufferLength: 300
    )
    
    /// Athletic/high-performance (precision mode)
    static let athletic = AlgorithmConfiguration(
        sampleRate: 44100.0,
        bufferSize: 0.1,
        minFreq: 10,
        maxFreq: 45,
        freqAccuracy: 0.025,
        lowerFormantFreq: 250,
        minAmp: 2.0e-4,
        downsampleFactorOverride: 35,
        correlationThreshold: 0.7,
        coarseStep: 3,
        fineSearchWindow: 8,
        decayRate: 0.75,
        maxRunLength: 5,
        leapThreshold: 0.20,
        targetBufferLength: 300
    )
    
    /// Research/clinical study mode
    static let research = AlgorithmConfiguration(
        sampleRate: 48000,
        bufferSize: 0.25,
        minFreq: 7,
        maxFreq: 40,
        freqAccuracy: 0.025,
        lowerFormantFreq: 250,
        minAmp: 2.0e-4,
        downsampleFactorOverride: 40,
        correlationThreshold: 0.75,
        coarseStep: 3,
        fineSearchWindow: 5,
        decayRate: 0.8,
        maxRunLength: 5,
        leapThreshold: 0.20,
        targetBufferLength: 600,
        minDataCheck: 100,
        pressureSlope: 1.119,
        pressureIntercept: -4.659,
        minValidFreq: 7.0,
        maxValidFreq: 40.0,
        minValidPressure: 6.0,
        maxValidPressure: 30.0,
        saveResults: true
    )
    
    /// Get configuration for a specific preset
    static func configuration(for preset: ConfigurationPreset) -> AlgorithmConfiguration {
        switch preset {
        case .standard:
            return .standard
        case .realTimeFeedback:
            return .realTimeFeedback
        case .highAccuracy:
            return .highAccuracy
        case .stableReadings:
            return .stableReadings
        case .lowPower:
            return .lowPower
        case .noisyEnvironment:
            return .noisyEnvironment
        case .pediatric:
            return .pediatric
        case .elderly:
            return .elderly
        case .athletic:
            return .athletic
        case .research:
            return .research
        case .custom:
            return .standard // Default to standard for custom
        }
    }
}
