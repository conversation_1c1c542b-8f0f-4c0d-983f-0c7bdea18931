//
//  BreathPhaseManager.swift
//  Breath2
//
//  Manages breath phases and provides delayed-aware feedback
//

import Foundation
import SwiftUI

// MARK: - Breath Phase Types

enum BreathPhase {
    case idle
    case preparing      // User about to start
    case detecting      // 0.5-1.5s - building buffer
    case active         // 1.5s+ - reliable readings
    case completed      // Breath finished
    case recovery       // Brief pause before next breath
}

enum BreathGuidance {
    case startGentle
    case maintainSteady
    case increaseSlightly
    case decreaseSlightly
    case holdCurrent
    case goodRange
    case tooLow
    case tooHigh
    case breathComplete
}

// MARK: - Breath Phase Manager

class BreathPhaseManager: ObservableObject {
    
    // MARK: - Published Properties
    
    @Published var currentPhase: BreathPhase = .idle
    @Published var currentGuidance: BreathGuidance = .startGentle
    @Published var phaseProgress: Double = 0.0
    @Published var breathDuration: TimeInterval = 0.0
    @Published var targetPressureRange: ClosedRange<Float> = 10.0...20.0
    @Published var isInTargetRange: Bool = false
    @Published var confidenceLevel: Float = 0.0
    
    // MARK: - Private Properties
    
    private var breathStartTime: Date?
    private var phaseStartTime: Date?
    private var lastSignificantReading: Float = 0.0
    private var readingHistory: [Float] = []
    private var stableReadingCount: Int = 0
    
    // Phase timing constants
    private let detectingPhaseDuration: TimeInterval = 1.0
    private let targetBreathDuration: TimeInterval = 3.5
    private let recoveryDuration: TimeInterval = 1.0
    
    // MARK: - Public Methods
    
    /// Process new pressure reading and update breath phase
    func processReading(pressure: Float, frequency: Float, audioLevel: Float) {
        let now = Date()
        
        // Detect breath start based on audio level increase
        if currentPhase == .idle && audioLevel > 0.01 {
            startBreath(at: now)
        }
        
        // Update current phase based on timing and readings
        updatePhase(pressure: pressure, frequency: frequency, audioLevel: audioLevel, at: now)
        
        // Generate guidance based on current phase and readings
        updateGuidance(pressure: pressure, frequency: frequency)
        
        // Update progress indicators
        updateProgress(at: now)
    }
    
    /// Reset to idle state
    func reset() {
        currentPhase = .idle
        currentGuidance = .startGentle
        phaseProgress = 0.0
        breathDuration = 0.0
        isInTargetRange = false
        confidenceLevel = 0.0
        breathStartTime = nil
        phaseStartTime = nil
        readingHistory.removeAll()
        stableReadingCount = 0
    }
    
    // MARK: - Private Methods
    
    private func startBreath(at time: Date) {
        breathStartTime = time
        phaseStartTime = time
        currentPhase = .preparing
        readingHistory.removeAll()
        stableReadingCount = 0
        
        // Brief preparation phase
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            if self.currentPhase == .preparing {
                self.transitionToDetecting(at: Date())
            }
        }
    }
    
    private func transitionToDetecting(at time: Date) {
        currentPhase = .detecting
        phaseStartTime = time
        currentGuidance = .maintainSteady
    }
    
    private func updatePhase(pressure: Float, frequency: Float, audioLevel: Float, at time: Date) {
        guard let breathStart = breathStartTime else { return }
        
        let breathElapsed = time.timeIntervalSince(breathStart)
        breathDuration = breathElapsed
        
        switch currentPhase {
        case .idle:
            break
            
        case .preparing:
            // Handled by timer in startBreath
            break
            
        case .detecting:
            // Transition to active when we have reliable readings or enough time has passed
            if breathElapsed > detectingPhaseDuration || (frequency > 0 && pressure > 5.0) {
                currentPhase = .active
                phaseStartTime = time
            }
            
        case .active:
            // Check for breath completion
            if audioLevel < 0.005 || breathElapsed > targetBreathDuration + 1.0 {
                completeBreath(at: time)
            }
            
        case .completed:
            // Transition to recovery
            if breathElapsed > targetBreathDuration + 0.5 {
                currentPhase = .recovery
                phaseStartTime = time
            }
            
        case .recovery:
            // Return to idle after recovery period
            if let phaseStart = phaseStartTime,
               time.timeIntervalSince(phaseStart) > recoveryDuration {
                reset()
            }
        }
    }
    
    private func completeBreath(at time: Date) {
        currentPhase = .completed
        phaseStartTime = time
        currentGuidance = .breathComplete
        
        // Calculate final metrics
        calculateBreathMetrics()
    }
    
    private func updateGuidance(pressure: Float, frequency: Float) {
        // Update reading history (less frequently to avoid jitter)
        if pressure > 0 {
            readingHistory.append(pressure)
            if readingHistory.count > 5 { // Smaller buffer for stability
                readingHistory.removeFirst()
            }
            lastSignificantReading = pressure
        }

        // Calculate confidence based on reading stability
        updateConfidence()

        // Determine if in target range (with small buffer to avoid rapid changes)
        let bufferZone: Float = 1.0 // 1 cm H2O buffer
        if pressure > 0 {
            if pressure >= (targetPressureRange.lowerBound - bufferZone) &&
               pressure <= (targetPressureRange.upperBound + bufferZone) {
                isInTargetRange = true
            } else if pressure < (targetPressureRange.lowerBound - bufferZone * 2) ||
                      pressure > (targetPressureRange.upperBound + bufferZone * 2) {
                isInTargetRange = false
            }
            // Keep previous state if in buffer zone to avoid flashing
        }

        // Simplified guidance - only change when really needed
        if pressure == 0 {
            currentGuidance = .startGentle
        } else if isInTargetRange {
            currentGuidance = .goodRange
        } else if pressure < targetPressureRange.lowerBound {
            currentGuidance = .increaseSlightly
        } else if pressure > targetPressureRange.upperBound {
            currentGuidance = .decreaseSlightly
        }
    }
    
    private func updateProgress(at time: Date) {
        guard let breathStart = breathStartTime else {
            phaseProgress = 0.0
            return
        }
        
        let elapsed = time.timeIntervalSince(breathStart)
        
        switch currentPhase {
        case .idle, .recovery:
            phaseProgress = 0.0
            
        case .preparing:
            phaseProgress = min(elapsed / 0.3, 1.0)
            
        case .detecting:
            phaseProgress = min(elapsed / detectingPhaseDuration, 1.0)
            
        case .active:
            phaseProgress = min(elapsed / targetBreathDuration, 1.0)
            
        case .completed:
            phaseProgress = 1.0
        }
    }
    
    private func updateConfidence() {
        if readingHistory.count < 3 {
            confidenceLevel = 0.0
            return
        }
        
        // Calculate stability of recent readings
        let recentReadings = Array(readingHistory.suffix(5))
        let average = recentReadings.reduce(0, +) / Float(recentReadings.count)
        let variance = recentReadings.map { pow($0 - average, 2) }.reduce(0, +) / Float(recentReadings.count)
        let stability = max(0, 1.0 - variance / 100.0) // Normalize variance
        
        // Confidence increases with stability and reading count
        let dataConfidence = min(Float(readingHistory.count) / 10.0, 1.0)
        confidenceLevel = stability * dataConfidence
    }
    
    private func calculateBreathMetrics() {
        // Calculate average pressure, consistency, etc.
        // This could be used for session analysis
    }
}

// MARK: - UI Helper Extensions

extension BreathPhaseManager {
    
    var phaseDisplayName: String {
        switch currentPhase {
        case .idle: return "Ready"
        case .preparing: return "Get Ready"
        case .detecting: return "Detecting..."
        case .active: return "Breathing"
        case .completed: return "Complete"
        case .recovery: return "Rest"
        }
    }
    
    var guidanceMessage: String {
        switch currentGuidance {
        case .startGentle: return "Start with gentle, steady pressure"
        case .maintainSteady: return "Maintain steady pressure"
        case .increaseSlightly: return "Increase pressure slightly"
        case .decreaseSlightly: return "Ease up a little"
        case .holdCurrent: return "Perfect! Hold this level"
        case .goodRange: return "Excellent pressure range"
        case .tooLow: return "Pressure too low"
        case .tooHigh: return "Pressure too high"
        case .breathComplete: return "Great breath!"
        }
    }
    
    var phaseColor: Color {
        switch currentPhase {
        case .idle: return .gray
        case .preparing: return .yellow
        case .detecting: return .orange
        case .active: return isInTargetRange ? .green : .blue
        case .completed: return .green
        case .recovery: return .gray
        }
    }
}
