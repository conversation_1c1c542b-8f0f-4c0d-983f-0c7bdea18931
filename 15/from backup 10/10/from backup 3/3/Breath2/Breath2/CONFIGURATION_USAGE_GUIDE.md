# Algorithm Configuration System - Usage Guide

## Overview
The new configuration system centralizes all algorithm parameters in one place, making it easy to tweak, test, and adjust settings without hunting through multiple files.

## Quick Start

### 1. Basic Usage
```swift
// Use a preset configuration
let config = AlgorithmConfiguration.realTimeFeedback
let pitchDetector = PitchDetector(configuration: config)

// Or create a custom configuration
var customConfig = AlgorithmConfiguration.standard
customConfig.bufferSize = 0.08  // Faster updates
customConfig.correlationThreshold = 0.55  // More sensitive
let detector = PitchDetector(configuration: customConfig)
```

### 2. Using Configuration Manager
```swift
// Create configuration manager (handles saving/loading)
let configManager = ConfigurationManager()

// Apply a preset
configManager.applyPreset(.highAccuracy)

// Make custom adjustments
configManager.adjustLatency(.ultraLow)
configManager.adjustSmoothness(.heavy)

// Use in AudioManager
let audioManager = AudioManager(configurationManager: configManager)
```

### 3. Runtime Configuration Updates
```swift
// Update configuration while running
var newConfig = audioManager.configurationManager.currentConfiguration
newConfig.correlationThreshold = 0.7
audioManager.updateConfiguration(newConfig)
```

## Available Presets

### Standard Presets
- **`.standard`** - Paper specifications (balanced)
- **`.realTimeFeedback`** - Fast updates, low latency
- **`.highAccuracy`** - Better precision, higher latency
- **`.stableReadings`** - Smooth output, slower response
- **`.lowPower`** - Reduced CPU usage
- **`.noisyEnvironment`** - Better noise rejection

### Specialized Presets
- **`.pediatric`** - Optimized for children
- **`.elderly`** - Stability focus for COPD patients
- **`.athletic`** - High-performance mode
- **`.research`** - Clinical study mode

## Key Parameters for Tweaking

### For Faster UI Updates
```swift
var config = AlgorithmConfiguration.standard
config.bufferSize = 0.05           // 50ms buffers (20 Hz updates)
config.correlationThreshold = 0.5  // Lower threshold for more detections
config.targetBufferLength = 200    // Smaller accumulation buffer
```

### For Better Accuracy
```swift
var config = AlgorithmConfiguration.standard
config.bufferSize = 0.2                    // Larger buffers for more data
config.correlationThreshold = 0.7          // Higher threshold for reliability
config.downsampleFactorOverride = 35       // Less downsampling
config.fineSearchWindow = 7                // More thorough search
```

### For Smoother Output
```swift
var config = AlgorithmConfiguration.standard
config.decayRate = 0.9              // Heavy smoothing
config.maxRunLength = 7             // Longer stability period
config.leapThreshold = 0.15         // Stricter change detection
```

### For Lower CPU Usage
```swift
var config = AlgorithmConfiguration.standard
config.downsampleFactorOverride = 60   // More aggressive downsampling
config.coarseStep = 4                  // Faster coarse search
config.bufferSize = 0.15               // Less frequent processing
```

## Configuration UI Integration

### Add to Your App
1. Add `ConfigurationView` to your app's navigation
2. Present as a sheet or push to navigation stack
3. Users can adjust settings in real-time

```swift
// In your main view
.sheet(isPresented: $showingConfiguration) {
    ConfigurationView()
}

// Or in a settings tab
NavigationLink("Algorithm Settings") {
    ConfigurationView()
}
```

### Custom Configuration Controls
```swift
struct CustomConfigView: View {
    @StateObject private var configManager = ConfigurationManager()
    
    var body: some View {
        VStack {
            // Quick latency adjustment
            Picker("Latency", selection: $selectedLatency) {
                ForEach(LatencyMode.allCases, id: \.self) { mode in
                    Text(mode.rawValue).tag(mode)
                }
            }
            .onChange(of: selectedLatency) {
                configManager.adjustLatency(selectedLatency)
            }

            // Custom slider for correlation threshold
            Slider(value: $correlationThreshold, in: 0.3...0.9) {
                Text("Sensitivity")
            }
            .onChange(of: correlationThreshold) {
                var config = configManager.currentConfiguration
                config.correlationThreshold = correlationThreshold
                configManager.updateConfiguration(config)
            }
        }
    }
}
```

## Testing Different Configurations

### A/B Testing Setup
```swift
class ConfigurationTester {
    let configurations = [
        "Standard": AlgorithmConfiguration.standard,
        "Fast": AlgorithmConfiguration.realTimeFeedback,
        "Accurate": AlgorithmConfiguration.highAccuracy,
        "Smooth": AlgorithmConfiguration.stableReadings
    ]
    
    func testConfiguration(_ name: String, with audioData: [Float]) -> TestResults {
        guard let config = configurations[name] else { return TestResults() }
        
        let detector = PitchDetector(configuration: config)
        let startTime = CFAbsoluteTimeGetCurrent()
        
        let detectedPitch = detector.processChunk(audioData)
        
        let processingTime = CFAbsoluteTimeGetCurrent() - startTime
        
        return TestResults(
            configName: name,
            detectedPitch: detectedPitch,
            processingTime: processingTime,
            memoryUsage: getCurrentMemoryUsage()
        )
    }
}
```

### Performance Monitoring
```swift
// Monitor performance with different settings
configManager.updatePerformanceMetrics(
    processingTime: processingTime,
    memoryUsage: memoryUsage,
    detectionRate: detectionRate,
    latency: latency,
    cpuUsage: cpuUsage
)

// Check if configuration meets targets
if configManager.meetsPerformanceTargets() {
    print("✅ Configuration performing well")
} else {
    print("⚠️ Consider adjusting settings")
}
```

## Export/Import Configurations

### Export Current Settings
```swift
if let jsonString = configManager.exportConfiguration() {
    // Save to file, share, or copy to clipboard
    UIPasteboard.general.string = jsonString
}
```

### Import Settings
```swift
let jsonString = """
{
  "sampleRate": 44100,
  "bufferSize": 0.08,
  "correlationThreshold": 0.65,
  "decayRate": 0.85,
  ...
}
"""

if configManager.importConfiguration(from: jsonString) {
    print("Configuration imported successfully")
}
```

## Validation and Safety

### Always Validate
```swift
let validation = config.validate()
if !validation.isValid {
    print("Configuration errors:")
    validation.errors.forEach { print("- \($0)") }
}
```

### Safe Parameter Ranges
- **Buffer Size**: 0.05 - 0.5 seconds
- **Correlation Threshold**: 0.3 - 0.9
- **Decay Rate**: 0.5 - 0.95
- **Frequency Range**: 3 - 60 Hz
- **Sample Rate**: Must satisfy Nyquist criterion

## Common Use Cases

### Real-Time Biofeedback
```swift
let config = AlgorithmConfiguration.realTimeFeedback
// Fast updates for immediate visual feedback
```

### Clinical Data Collection
```swift
let config = AlgorithmConfiguration.research
config.saveResults = true
// High accuracy with data logging
```

### Battery-Conscious Mobile App
```swift
let config = AlgorithmConfiguration.lowPower
// Reduced CPU usage for longer battery life
```

### Noisy Environment (Hospital/Clinic)
```swift
let config = AlgorithmConfiguration.noisyEnvironment
// Better noise rejection for clinical settings
```

## Troubleshooting

### Configuration Not Taking Effect
- Ensure you're calling `updateConfiguration()` on AudioManager
- Check that validation passes
- Restart audio engine if sample rate changed

### Performance Issues
- Monitor `ConfigurationManager.performanceMetrics`
- Use `.lowPower` preset as starting point
- Increase buffer size and downsample factor

### Unstable Readings
- Increase `correlationThreshold`
- Increase `decayRate` for more smoothing
- Reduce `leapThreshold` for stricter consistency

### Missing Detections
- Decrease `correlationThreshold`
- Extend frequency range (`minFreq`, `maxFreq`)
- Increase `targetBufferLength` for more data

## Best Practices

1. **Start with presets** - Use existing presets as starting points
2. **Validate changes** - Always validate configuration before applying
3. **Test incrementally** - Make small adjustments and test
4. **Monitor performance** - Watch CPU usage and detection rates
5. **Save working configs** - Export configurations that work well
6. **Document changes** - Keep notes on what works for different scenarios

## Integration with Existing Code

The configuration system is designed to be backward compatible. Existing code will continue to work, but you can gradually migrate to the new system:

```swift
// Old way (still works)
let detector = PitchDetector(
    sampleRate: 44100,
    minFreq: 7,
    maxFreq: 40,
    // ... other parameters
)

// New way (recommended)
let config = AlgorithmConfiguration.standard
let detector = PitchDetector(configuration: config)
```

This system gives you complete control over algorithm behavior while maintaining the scientific rigor of the original paper specifications.
